from typing import Dict, List, Optional, Tuple

import logging
import os

import numpy as np
import pycuda.autoinit  # pylint: disable=unused-import
import pycuda.driver as cuda
import tensorrt as trt

from utils import trt_utils


class HostDeviceMem(object):
    def __init__(self, host_mem, device_mem):
        self.host = host_mem
        self.device = device_mem

    def __str__(self):
        return "Host:\n" + str(self.host) + "\nDevice:\n" + str(self.device)

    def __repr__(self):
        return self.__str__()


class TensorRTInference(object):
    def __init__(self, engine_file: str):
        self.engine_file = engine_file
        self.trt_logger: Optional[trt.Logger] = None
        self.engine: Optional[trt.ICudaEngine] = None
        self.context: Optional[trt.IExecutionContext] = None

        self.input_bindings: Dict[str, HostDeviceMem] = dict()
        self.input_shapes: Dict[str, Tuple[int]] = dict()
        self.input_dtypes: Dict[str, np.dtype] = dict()
        self.output_bindings: Dict[str, HostDeviceMem] = dict()
        self.output_shapes: Dict[str, Tuple[int]] = dict()
        self.output_dtypes: Dict[str, np.dtype] = dict()
        self.gpu_buffers: List[int] = list()
        self.binding_idx: Dict[str, int] = dict()

    def load(self):
        # load engine
        self.trt_logger = trt.Logger(trt.Logger.Severity.INFO)
        # load plugin 
        trt.init_libnvinfer_plugins(self.trt_logger, "")
        with open(self.engine_file, 'rb') as f_engine, trt.Runtime(self.trt_logger) as runtime:
            self.engine = runtime.deserialize_cuda_engine(f_engine.read())

        # allocate resources
        self.context = self.engine.create_execution_context()

        for i, binding in enumerate(self.engine):
            self.binding_idx[binding] = i

            # allocate cpu and gpu buffers
            if trt_utils.is_trt_10():
                shape = tuple(self.engine.get_tensor_shape(binding))
                dtype = np.dtype(trt.nptype(self.engine.get_tensor_dtype(binding)))
            else:
                shape = tuple(self.engine.get_binding_shape(binding))
                dtype = np.dtype(trt.nptype(self.engine.get_binding_dtype(binding)))
            size = trt.volume(shape)
            host_mem = cuda.pagelocked_empty(size, dtype)
            device_mem = cuda.mem_alloc(host_mem.nbytes)
            self.gpu_buffers.append(int(device_mem))

            if trt_utils.is_trt_10():
                is_input = self.engine.get_tensor_mode(binding) == trt.TensorIOMode.INPUT
            else:
                is_input = self.engine.binding_is_input(binding)
            if is_input:
                self.input_bindings[binding] = HostDeviceMem(host_mem, device_mem)
                self.input_shapes[binding] = shape
                self.input_dtypes[binding] = dtype
            else:
                self.output_bindings[binding] = HostDeviceMem(host_mem, device_mem)
                self.output_shapes[binding] = shape
                self.output_dtypes[binding] = dtype

    def infer(self, inputs: Dict[str, np.ndarray]):
        # transfer data to GPU
        for input_name in self.input_bindings.keys():
            assert input_name in inputs, 'Necessary input %s not found' % input_name
            arr = inputs[input_name]
            assert arr.shape == self.input_shapes[input_name], \
                'Shape mismatch for input %s, need %s, get %s' % \
                (input_name, str(self.input_shapes[input_name]), str(arr.shape))
            assert arr.dtype == self.input_dtypes[input_name], \
                'Dtype mismatch for input %s, need %s, get %s' % \
                (input_name, str(self.input_dtypes[input_name]), str(arr.dtype))
            np.copyto(self.input_bindings[input_name].host, np.ravel(arr))
            cuda.memcpy_htod(self.input_bindings[input_name].device,
                             self.input_bindings[input_name].host)

        # inference
        self.context.execute_v2(self.gpu_buffers)

        # transfer data back to CPU
        output_data = dict()
        # transfer data back to CPU
        for output_name, binding in self.output_bindings.items():
            cuda.memcpy_dtoh(binding.host, binding.device)
            arr = np.reshape(binding.host.copy(), self.output_shapes[output_name])
            output_data[output_name] = arr

        return output_data

    def infer_output(self):
        # return engine output
        output_dict = list()
        for output_name, binding in self.output_bindings.items():
             output_dict.append(output_name)
        return output_dict

