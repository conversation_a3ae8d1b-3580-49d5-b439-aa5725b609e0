import json
import logging
import sys
import pynvml

import tensorrt as trt

from utils import trt_utils

DEFAULT_WORKSPACE_VALUE = 12 * 1 << 30  # 12GB

def _get_device_architecture():
    # 获取显卡架构
    pynvml.nvmlInit()
    gpu_id = 0
    handle = pynvml.nvmlDeviceGetHandleByIndex(gpu_id)
    # get chip architecture
    # NVML_DEVICE_ARCH_KEPLER    2 // Devices based on the NVIDIA Kepler architecture
    # NVML_DEVICE_ARCH_MAXWELL   3 // Devices based on the NVIDIA Maxwell architecture
    # NVML_DEVICE_ARCH_PASCAL    4 // Devices based on the NVIDIA Pascal architecture
    # NVML_DEVICE_ARCH_VOLTA     5 // Devices based on the NVIDIA Volta architecture
    # NVML_DEVICE_ARCH_TURING    6 // Devices based on the NVIDIA Turing architecture
    # NVML_DEVICE_ARCH_AMPERE    7 // Devices based on the NVIDIA Ampere architecture
    # NVML_DEVICE_ARCH_UNKNOWN   0xffffffff // Anything else, presumably something newer
    gpu_device_arch = pynvml.nvmlDeviceGetArchitecture(handle)
    pynvml.nvmlShutdown()

    return gpu_device_arch

def build_trt_engine(onnx_file: str, fp16: int, refit: int, output_engine_file: str, user_item_param_str: str):
    logging.info('Building TensorRT engine %s from ONNX file %s...' % (output_engine_file, onnx_file))
    trt_logger = trt.Logger(trt.Logger.Severity.INFO)

    if trt_utils.is_trt_10():
        network_creation_flag = (1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
        trt.init_libnvinfer_plugins(trt_logger, "")
    else:
        network_creation_flag = (1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH)) | \
                                (1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_PRECISION))

    with trt.Builder(trt_logger) as builder, \
            builder.create_network(network_creation_flag) as network, \
            trt.OnnxParser(network, trt_logger) as parser:

        if not trt_utils.is_trt_10():
            builder.max_batch_size = 1

        # parse onnx file
        with open(onnx_file, 'rb') as f_onnx:
            if not parser.parse(f_onnx.read()):
                logging.error('ONNX file parse error:')
                for i in range(parser.num_errors):
                    logging.error('Error %d: %s', i, parser.get_error(i))

                raise Exception('ONNX file parse error')

        builder_config = builder.create_builder_config()
        if not trt_utils.is_trt_10():
            builder_config.max_workspace_size = DEFAULT_WORKSPACE_VALUE
        builder_config.profiling_verbosity = trt.ProfilingVerbosity.DETAILED
        # Not supported in current version, commented out
        # builder_config.set_flag(trt.BuilderFlag.VERSION_COMPATIBLE)

        # gpu_arch = _get_device_architecture()
        # logging.info('Building TensorRT gpu device architecture %s' % gpu_arch)
        # # only Ampere architecture set hardware_compatibility_level config
        # if gpu_arch == pynvml.NVML_DEVICE_ARCH_AMPERE:
        #     builder_config.hardware_compatibility_level = trt.HardwareCompatibilityLevel.AMPERE_PLUS

        if user_item_param_str != "{}":
            profile = builder.create_optimization_profile()
            user_item_param = json.loads(user_item_param_str)
            for key, value in user_item_param.items():
                logging.info('profile shape modified: %s,%s' % (key, str(tuple(value))))
                profile.set_shape(key, tuple(value), tuple(value), tuple(value))
            builder_config.add_optimization_profile(profile)

        if refit:
            logging.info('Turn on Refit')
            builder_config.set_flag(trt.BuilderFlag.REFIT)

        if fp16:
            logging.info('Build fp16 engine')
            builder_config.set_flag(trt.BuilderFlag.FP16)
            builder_config.set_flag(trt.BuilderFlag.OBEY_PRECISION_CONSTRAINTS)

        if trt_utils.is_trt_10():
            engine = builder.build_serialized_network(network, builder_config)
        else:
            engine = builder.build_engine(network, config=builder_config)
        if engine is None:
            raise Exception('TensorRT build engine fail')

        logging.info('Build TensorRT engine %s succeed' % output_engine_file)
        with open(output_engine_file, 'wb') as f_engine:
            if trt_utils.is_trt_10():
                f_engine.write(engine)
            else:
                f_engine.write(engine.serialize())


if __name__ == '__main__':
    logging.basicConfig(
        format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
        level=logging.INFO, stream=sys.stdout, force=True
    )

    onnx_file = sys.argv[1]
    fp16 = int(sys.argv[2])
    refit = int(sys.argv[3])
    output_engine_file = sys.argv[4]
    user_item_param_str = sys.argv[5]
    build_trt_engine(onnx_file, fp16, refit, output_engine_file, user_item_param_str)
