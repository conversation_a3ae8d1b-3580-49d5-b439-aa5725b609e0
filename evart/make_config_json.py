import json
import os
from typing import List

from components.const import REFITTABLE

def write_config_file(config_file: str, onnx_file: str, fp16: bool, batches: list, output_type: str,
                      opset_version: int):

    model_type = "ONNX"
    engine_type = "TRT"
    model_name = os.path.basename(onnx_file)
    model_path = os.path.dirname(onnx_file)
    if len(model_path) > 0:
        model_path += "/"

    precision = "F32"
    if fp16:
        precision = "F16"

    save_dict = dict()
    save_dict['modelName'] = model_name
    save_dict['modelType'] = model_type
    save_dict['modelPath'] = model_path # 需要'/'
    save_dict['precision'] = precision
    save_dict['engineType'] = engine_type
    save_dict['batches'] = batches
    save_dict['outputType'] = output_type
    save_dict['refit'] = REFITTABLE
    save_dict['opsetVersion'] = opset_version

    with open(config_file, 'w') as f:
        f.write(json.dumps(save_dict, indent=4))

if __name__ == '__main__':
    logging.basicConfig(
        format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
        level=logging.INFO, stream=sys.stdout, force=True
    )

    onnx_file = sys.argv[1]
    batches = [int(batch) for batch in sys.argv[2].split(';')]
    config_file = 'evart_config.json'
    fp16 = 0
    output_type = 'evart'
    opset_version = 9
    write_config_file(config_file, onnx_file, 0, batches, output_type, opset_version)
