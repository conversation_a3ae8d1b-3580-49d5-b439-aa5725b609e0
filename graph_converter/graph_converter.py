import logging
import os
from typing import Dict, List, Optional
import copy

import numpy as np
import onnx
import onnx_graphsurgeon as gs

os.environ['TF_CPP_MIN_LOG_LEVEL'] = '0'
import tensorflow as tf

from google.protobuf import text_format
from tensorflow.core.framework import graph_pb2, node_def_pb2, tensor_shape_pb2, types_pb2
from tensorflow.core.protobuf import config_pb2, meta_graph_pb2
from tensorflow.python.grappler import tf_optimizer
from tf2onnx import optimizer
from tf2onnx.convert import process_tf_graph

from components import const
from model_export import param_parser
from onnx_utils import benchmark_onnx

DEFAULT_OPTIMIZERS = ('pruning', 'debug_stripper', 'constfold', 'arithmetic', 'dependency', 'function')


class TFGraphConverter(object):
    def __init__(self, parameter_parser: param_parser.NumerousDenseParameterParser,
                 predict_target: List[str], placeholder_replace: Optional[List[str]],
                 batch_size_list: Optional[List[int]]):
        self.parameter_parser = parameter_parser
        self.model_path = parameter_parser.model_path
        self.predict_target = predict_target
        self.placeholder_replace = placeholder_replace
        self.batch_size_list = batch_size_list

        self.dense_slice_name = 'dense'
        self.parameter_parser.read_dense_parameters(self.dense_slice_name)

        self.session: Optional[tf.compat.v1.Session] = None
        self.graph_proto: Optional[graph_pb2.GraphDef] = None
        self.frozen_graph: Optional[graph_pb2.GraphDef] = None
        self.input_shape: Dict[str, List[int]] = dict()

    def _load_graph(self):
        graph_file = self.parameter_parser.model_slice[self.dense_slice_name].graph_file
        binary_graph_file = self.parameter_parser.model_slice[self.dense_slice_name].binary_graph_file

        if binary_graph_file is not None:
            # first try to load binary graph file
            logging.info('Loading binary graph file: %s...' % binary_graph_file)
            self.graph_proto = graph_pb2.GraphDef()
            with open(os.path.join(self.model_path, binary_graph_file), 'rb') as f:
                self.graph_proto.ParseFromString(f.read())
        else:
            # if binary graph file not exist, load pbtxt graph file
            logging.info('Loading graph file: %s...' % graph_file)
            with open(os.path.join(self.model_path, graph_file), 'r') as f:
                self.graph_proto = text_format.Parse(f.read(), graph_pb2.GraphDef())

    def _fix_control_placeholders(self):
        # enumerate all control placeholders
        control_placeholders = self.parameter_parser.control_placeholders[self.dense_slice_name]
        new_graph_def = tf.compat.v1.GraphDef()

        for i, node in enumerate(self.graph_proto.node):
            if node.op == 'Placeholder' and node.name in control_placeholders:
                cph_def = control_placeholders[node.name]
                const_node = node_def_pb2.NodeDef()
                const_node.name = node.name
                const_node.op = 'Const'
                const_node.attr['value'].tensor.tensor_shape.CopyFrom(
                    tensor_shape_pb2.TensorShapeProto(unknown_rank=False))
                if cph_def.cph_type == 'BOOL':
                    const_node.attr['dtype'].type = types_pb2.DT_BOOL
                    const_node.attr['value'].tensor.dtype = types_pb2.DT_BOOL
                    const_node.attr['value'].tensor.bool_val.append(cph_def.cph_value.item())
                elif cph_def.cph_type == 'INT':
                    const_node.attr['dtype'].type = types_pb2.DT_INT32
                    const_node.attr['value'].tensor.dtype = types_pb2.DT_INT32
                    const_node.attr['value'].tensor.int_val.append(cph_def.cph_value.item())
                elif cph_def.cph_type == 'FLOAT':
                    const_node.attr['dtype'].type = types_pb2.DT_FLOAT
                    const_node.attr['value'].tensor.dtype = types_pb2.DT_FLOAT
                    const_node.attr['value'].tensor.float_val.append(cph_def.cph_value.item())
                elif cph_def.cph_type == 'STRING':
                    const_node.attr['dtype'].type = types_pb2.DT_STRING
                    const_node.attr['value'].tensor.dtype = types_pb2.DT_STRING
                    const_node.attr['value'].tensor.string_val.append(cph_def.cph_value.item())

                logging.info('Fix control placeholder %s' % node.name)
                new_graph_def.node.append(const_node)
            else:
                new_graph_def.node.append(node)

        self.graph_proto = new_graph_def

    def _fix_dense_placeholder(self):
        for node in self.graph_proto.node:
            if node.op == 'Placeholder' and node.name in self.parameter_parser.dense_placeholders:
                if node.attr['shape'].shape.unknown_rank:
                    node.attr['shape'].shape.unknown_rank = False
                    node.attr['shape'].shape.dim.add().size = -1
                    node.attr['shape'].shape.dim.add().size = self.parameter_parser.dense_placeholders[node.name].size
                    logging.info('Fix dense placeholder shape {}'.format(node))

    def _fix_sparse_placeholder(self):
        name_to_node = dict()
        for node in self.frozen_graph.node:
            name_to_node[node.name] = node

        # iterate over matmul nodes
        replace_nodes = dict()
        for node in self.frozen_graph.node:
            if node.op == 'MatMul':
                ph_input = self._get_node_name(node.input[0])
                var_input = self._get_node_name(node.input[1])
                if ph_input not in self.parameter_parser.sparse_placeholders:
                    continue
                # sparse_ph 和table_ph 并非直接做 matmul, eg.show click
                table_name = self.parameter_parser.sparse_placeholders[ph_input].table_name
                if var_input != table_name:
                    logging.info('Not Fuse node: %s, placeholder name: %s, matmul tensor name: %s, table: %s' %
                                 (node.name, ph_input, var_input, table_name))
                    continue

                assert not self.parameter_parser.sparse_placeholders[ph_input].is_sparse, \
                    'Sparse placeholder not supported'

                shape = list()
                for dim in name_to_node[var_input].attr['shape'].shape.dim:
                    shape.append(dim.size)
                logging.info('Fuse node: %s, placeholder name: %s, sparse var name: %s, shape: %s' %
                             (node.name, ph_input, var_input, str(shape)))
                replace_nodes[node.name] = shape

        # replace matmul nodes with placeholders
        if len(replace_nodes) > 0:
            self._split_graph(list(replace_nodes.keys()), replace_nodes)

    def _reset_graph_device(self):
        for node in self.graph_proto.node:
            node.device = ''

    @staticmethod
    def _get_node_name(input_name: str):
        return input_name.split(':')[0].lstrip('^')

    def _clean_graph(self, graph_proto: graph_pb2.GraphDef, targets: Optional[List[str]],
                     exclude_nodes: Optional[List[str]] = None):
        """
        功能：清理冗余节点
        主要逻辑：
        1. 搜索出所有的assign节点
        2. 从targets节点向前搜索
        3. 如果遇到exclude_node则停止搜索
        """
        logging.info('Start to clean graph...')
        logging.info('Number of nodes before clean: %d' % len(graph_proto.node))

        node_dict = dict()
        assign_node = dict()
        for node in graph_proto.node:
            node_dict[node.name] = node
            if node.op in ['Assign', 'AssignVariableOp']:
                dest_node_name = self._get_node_name(node.input[0])
                if dest_node_name not in assign_node:
                    assign_node[dest_node_name] = list()
                assign_node[dest_node_name].append(node)

        visited = set()
        marked_nodes = set()

        def mark_node(node_name):
            if node_name in visited:
                return

            marked_nodes.add(node_name)
            visited.add(node_name)

            # 对于exclude_nodes，搜索到此为止，不继续上溯
            if exclude_nodes is not None and node_name in exclude_nodes:
                return

            for input_name in node_dict[node_name].input:
                next_node_name = self._get_node_name(input_name)
                mark_node(next_node_name)
            if node_name in assign_node:
                for node in assign_node[node_name]:
                    mark_node(node.name)

        for output_node_name in targets:
            mark_node(output_node_name)
        # 保留ReadVariableOp节点
        for node in graph_proto.node:
            if node.op == 'ReadVariableOp':
                mark_node(node.name)

        new_graph_def = tf.compat.v1.GraphDef()
        for node in graph_proto.node:
            if node.name in marked_nodes:
                new_graph_def.node.append(node)

        logging.info('Number of nodes in cleaned graph: %d' % len(new_graph_def.node))
        return new_graph_def

    def _freeze_graph(self):
        tf_graph = tf.Graph()
        with tf_graph.as_default():
            tf.compat.v1.import_graph_def(graph_def=self.graph_proto, name='')
            with tf.compat.v1.Session(graph=tf_graph) as sess:
                # fill feeds and fetches
                feed = dict()
                fetch = []
                for param_def in self.parameter_parser.parameters[self.dense_slice_name].values():
                    try:
                        assign_op = tf_graph.get_operation_by_name(self._get_node_name(param_def.update_op_name))
                        fetch.append(assign_op)
                    except KeyError:
                        logging.info('Skip assign op %s' % param_def.update_op_name)
                        continue
                    feed_key = '%s:0' % param_def.placeholder_name
                    weight = param_def.get_parameter()
                    feed[feed_key] = weight
                # assign weights at once
                sess.run(fetch, feed)

                # freeze graph
                # 图冻结，调用convert_variables_to_constants
                self.frozen_graph = tf.compat.v1.graph_util.convert_variables_to_constants(sess, self.graph_proto,
                                                                                           self.predict_target)

    @staticmethod
    def _save_graph(graph_def: graph_pb2.GraphDef, output_filename: str):
        with open(output_filename, 'wb') as f:
            f.write(graph_def.SerializeToString())

    def _parse_model_inputs(self):
        self.input_shape = dict()
        for node in self.frozen_graph.node:
            if node.op == 'Placeholder':
                node_name = node.name
                shape = [dim.size for dim in node.attr['shape'].shape.dim]
                self.input_shape[node_name] = shape

        logging.info('Detected %d inputs in model' % len(self.input_shape))

    def _check_cpu_targets_shape(self, shape1: dict(), shape2: dict()):
        # batch size 确定的情况下，cpu target 必须是固定维度的，不受两次赋值的影响
        cpu_targets = list()
        for node_name in self.placeholder_replace:
            if shape1[node_name] == shape2[node_name]:
                cpu_targets.append(node_name)
                logging.info('Node %s should be used as Cpu Target.' % node_name)
            else:
                logging.info('Node %s cannot be used as Cpu Target to graph, Please select Node without dynamic dimensions' % node_name)

        return cpu_targets

    def _fix_phtype_dynamic_node(self):
        # 引用方式，该函数对dict只读
        input_shape = self.input_shape
        for name, shape in input_shape.items():
            # fuse_output concat ph node: batch_size * key_size(unk) * emb_size
            if name in self.parameter_parser.phtype_fuse_concat:
                if shape[1] != -1:
                    self.parameter_parser.phtype_fuse_concat.discard(name)
        logging.info('Warning: Fuse_Concat nodes with dynamic dimensions are : %s ' % self.parameter_parser.phtype_fuse_concat)
        logging.info('Warning: Table nodes with dynamic dimensions are : %s ' % self.parameter_parser.phtype_table)
        logging.info('Warning: Placeholder nodes with dynamic dimensions are : %s ' % self.parameter_parser.phtype_ph)

    def get_sample_input(self, key_size=int, batch_size=5):
        # 当图中存在存在table_ph和非fuse的sparse_ph时，table_ph的第一维和sparse_ph的第二维为key size
        # key_size 是动态维度，需要两次赋值进行后续推导
        sample_input = dict()
        for name, shape in self.get_input_shape(key_size, batch_size).items():
            sample_array = np.random.rand(*shape).astype(np.float32)
            sample_input[name] = sample_array
        return sample_input

    def get_input_shape(self, key_size=int, batch_size=5):
        input_shape = copy.deepcopy(self.input_shape)
        for name, shape in input_shape.items():
            try:
                # conf 中存在三种情况，使node具有动态维度 (key_size & batch_size)
                if name in self.parameter_parser.phtype_fuse_concat:
                    # fuse_output concat ph node: batch_size * key_size(unk) * emb_size
                    shape[0] = batch_size
                    shape[1] = key_size
                elif name in self.parameter_parser.phtype_table:
                    # table ph node: key_size * emb_size
                    shape[0] = key_size
                elif name in self.parameter_parser.phtype_ph:
                    # sparse ph node: batch_size * key_size
                    shape[0] = batch_size
                    shape[1] = key_size
                else:
                    shape[0] = batch_size
            except IndexError:
                logging.info(f"{name} input shape error: {shape}")
        return input_shape

    def _run_graph_grappler(self, graph_def: graph_pb2.GraphDef, optimizers=DEFAULT_OPTIMIZERS):
        # run grappler
        logging.info('Running grappler...')

        tf.compat.v1.reset_default_graph()
        with tf.compat.v1.Session():
            # 运行tf图优化
            config = config_pb2.ConfigProto()
            config.graph_options.infer_shapes = True
            config.graph_options.rewrite_options.optimizers.extend(optimizers)

            meta_graph = tf.compat.v1.train.export_meta_graph(graph_def=graph_def)
            fetch_collection = meta_graph_pb2.CollectionDef()
            fetch_collection.node_list.value.extend([k + ':0' for k in self.input_shape.keys()])
            fetch_collection.node_list.value.extend([k + ':0' for k in self.predict_target])
            meta_graph.collection_def['train_op'].CopyFrom(fetch_collection)
            return tf_optimizer.OptimizeGraph(config, meta_graph)

    def export_to_onnx(self, batch_size: int, output_path: str, engine_type: str, opset_version: int,
                       convert_with_refit: bool, use_user_item_inputs=False):
        """
        功能：根据传入的batch size，生成batch size固定的ONNX文件到指定路径
        主要逻辑：
        1. 拷贝frozen graph
        2. 运行grappler
        3. 调用process_tf_graph生成ONNX模型
        4. 运行ONNX优化
        5. 将ONNX的batch_size写死
        6. 将ONNX文件保存至指定目录下
        """

        # fix frozen graph batch size
        frozen_graph = graph_pb2.GraphDef()
        frozen_graph.CopyFrom(self.frozen_graph)

        # trt不用evart工具转engine时，将batch_size写死(trt分支)
        if not const.TRT_GENERATE_ENGINE_BY_EVART_TOOL and engine_type == 'trt' and not convert_with_refit:
            logging.info('TensorRT Engine set tf model input dim[0] as batch %d' % batch_size)
            for node in frozen_graph.node:
                if node.op == 'Placeholder':
                    if node.name in self.parameter_parser.phtype_table:
                        # table ph node: key_size * emd_size
                        continue
                    if not use_user_item_inputs:
                        if node.attr['shape'].shape.dim[0].size == -1:
                            node.attr['shape'].shape.dim[0].size = batch_size

        # run grappler
        frozen_graph = self._run_graph_grappler(frozen_graph)

        file_path, _ = os.path.split(output_path)
        grappler_file = os.path.join(file_path, 'frozen_graph_grappler_%d.pb' % batch_size)
        self._save_graph(frozen_graph, grappler_file)
        logging.info('Save grappler %s ...' % grappler_file)

        # tf2onnx
        input_target = ';'.join(self.input_shape.keys())
        predict_target = ';'.join(self.predict_target)
        benchmark_onnx.export_onnx(grappler_file, output_path, batch_size, input_target, predict_target, engine_type,
                                   opset_version)

        # delete isolated ph nodes
        self._trim_onnx(output_path)

        # ort shape infer
        logging.info('Running shape inference on ONNX file: %s' % output_path)
        benchmark_onnx.infer_onnx_shape(output_path)

    def _trim_onnx(self, output_path: str):
        onnx_model = onnx.load(output_path)

        graph = gs.import_onnx(onnx_model)

        # 找到所有孤立的placeholder节点
        isolated_placeholders = []
        for tensor in graph.inputs:
            # 检查这个tensor是否被任何节点使用
            if not any(tensor in node.inputs for node in graph.nodes):
                logging.info('Found isolated ph node: %s' % tensor.name)
                isolated_placeholders.append(tensor)

        # 删除孤立的placeholder节点
        for tensor in isolated_placeholders:
            graph.inputs.remove(tensor)

        # 更新图
        graph.cleanup().toposort()

        # 将修改后的图转换回ONNX模型
        onnx_model = gs.export_onnx(graph)

        # 保存修改后的ONNX模型
        onnx.save(onnx_model, output_path)

    def _infer_shape(self, graph_def: graph_pb2.GraphDef, infer_keys: List[str], key_size: int):
        # 当图中存在存在table_ph和非fuse的sparse_ph时，table_ph的第一维和sparse_ph的第二维为key size
        # key_size 是动态维度，需要两次赋值进行后续推导
        with tf.Graph().as_default() as tf_graph:
            tf.import_graph_def(graph_def=graph_def, name='')
            with tf.compat.v1.Session(graph=tf_graph) as sess:
                output_tensors = [tf_graph.get_tensor_by_name(k + ':0') for k in infer_keys]
                feed_dict = {k + ':0': v for k, v in self.get_sample_input(key_size, batch_size=1).items()}
                output_arrays = sess.run(output_tensors, feed_dict)

                output_shape = dict()
                # TODO: cpu target 的第一维如果不为bs, 推理错误
                for k, v in zip(infer_keys, output_arrays):
                    shape = list(v.shape)
                    shape[0] = -1
                    output_shape[k] = shape

                return output_shape

    def _split_graph(self, replace_nodes: List[str], shape: Dict[str, List[int]]):
        # split graph and replace nodes to placeholder
        graph_after_replace = self._clean_graph(self.frozen_graph, targets=self.predict_target,
                                                exclude_nodes=replace_nodes)

        new_graph_def = tf.compat.v1.GraphDef()
        for node in graph_after_replace.node:
            if node.name in replace_nodes:
                placeholder_node = node_def_pb2.NodeDef()
                placeholder_node.name = node.name
                placeholder_node.op = 'Placeholder'
                placeholder_node.attr['dtype'].type = types_pb2.DT_FLOAT
                for dim_val in shape[node.name]:
                    dim = tensor_shape_pb2.TensorShapeProto.Dim()
                    dim.size = dim_val
                    placeholder_node.attr['shape'].shape.dim.append(dim)
                new_graph_def.node.append(placeholder_node)
                logging.info('Write %s as placeholder node to graph...' % node.name)
            else:
                new_graph_def.node.append(node)

        self.frozen_graph = new_graph_def

    def _enumerate_node_output_name(self, op_name: str, output_count=1):
        node_output_names = list()
        for node in self.frozen_graph.node:
            if node.op == op_name:
                if output_count > 1:
                    for i in range(output_count):
                        node_output_names.append(node.name + ':' + str(i))
                else:
                    node_output_names.append(node.name)

        # sort
        node_output_names.sort()
        return node_output_names

    def _check_node_values(self, node_name_list: list(), values: dict()):
        # 检测不同 batch size 和 key_size 下，节点的结果是否一致
        for node_name in node_name_list:
            for i in range(1, len(values[node_name])):
                # shape 和 value 都要一致
                output1 = values[node_name][i - 1]
                output2 = values[node_name][i]
                if not np.array_equal(output1, output2):
                    logging.info('BroadcastGradientArgs node output name %s result [%d] vs [%d] have the different shape: %s vs %s and value: %s vs %s ' % \
                        (node_name, i - 1, i, list(output1.shape), list(output2.shape), output1, output2))
                    return False

            logging.info('Node output name %s have the same shape: %s and value: %s ' % \
                         (node_name, list(values[node_name][0].shape), values[node_name][0]))
        return True

    def _infer_values(self, graph_def: graph_pb2.GraphDef, infer_keys: List[str], key_size: int, batch_size: int):
        # 当图中存在存在table_ph和非fuse的sparse_ph时，table_ph的第一维和sparse_ph的第二维为key size
        # key_size 是动态维度，需要两次赋值进行后续推导
        with tf.Graph().as_default() as tf_graph:
            tf.import_graph_def(graph_def=graph_def, name='')
            with tf.compat.v1.Session(graph=tf_graph) as sess:
                feed_dict = {k + ':0': v for k, v in self.get_sample_input(key_size, batch_size).items()}
                output_values = dict()

                output_arrays = sess.run(infer_keys, feed_dict)

                for k, v in zip(infer_keys, output_arrays):
                    output_values[k] = v

                return output_values

    def _make_const_node(self, node_name, node_dtype, node_value):
        const_node = node_def_pb2.NodeDef()
        const_node.name = node_name
        const_node.op = 'Const'
        const_node.attr['dtype'].type = node_dtype
        const_node.attr['value'].tensor.tensor_shape.CopyFrom(
            tensor_shape_pb2.TensorShapeProto(dim=[tensor_shape_pb2.TensorShapeProto.Dim(size=len(node_value))]))
        const_node.attr['value'].tensor.dtype = node_dtype
        if node_dtype == types_pb2.DT_FLOAT:
            for v in node_value:
                value = float(v)
                const_node.attr['value'].tensor.float_val.append(value)
        elif node_dtype == types_pb2.DT_INT32:
            for v in node_value:
                value = int(v)
                const_node.attr['value'].tensor.int_val.append(value)
        else:
            logging.info('Not support, node name: %s , node dtype: %s ' % (node_name, node_dtype))
            raise Exception('Not support node dtype.')

        return const_node

    def _convert_frozen_graph(self):
        # 1.遍历图中所有就op 为 BroadcastGradientArgs 的节点，并记录
        # 2.更改 key_size 和 batch_size 验证这些节点的 shape 和 value 是否固定，
        # 3.如果固定，遍历图节点，把所有输入包含 BroadcastGradientArgs 节点的输入替换为 constant,
        #   constant 的 shape 和 value 为 BroadcastGradientArgs 节点推理出来的值
        # 4.如果不固定，图优化失败，输出日志

        # enumerate all BroadcastGradientArgs
        output_names = list()
        output_names = self._enumerate_node_output_name('BroadcastGradientArgs', output_count=2)
        if len(output_names) == 0:
            return

        logging.info('BroadcastGradientArgs node output name: %s ' % output_names)

        batch_sizes = dict()
        for batch_size in self.batch_size_list:
            key_sizes = [batch_size, batch_size * 2]
            batch_sizes[batch_size] = key_sizes
        logging.info('infer batch and key size: %s ' % batch_sizes)

        # 存储所有节点的结果
        values = dict()
        for batch_size, key_sizes in batch_sizes.items():
            for key_size in key_sizes:
                value = self._infer_values(self.frozen_graph, output_names, key_size, batch_size)
                for k, output in value.items():
                    if k in values:
                        values[k].append(output)
                    else:
                        outputs = list()
                        outputs.append(output)
                        values[k] = outputs

        result = self._check_node_values(output_names, values)
        if not result:
            raise Exception('BroadcastGradientArgs node have different value.')
        else:
            logging.info('BroadcastGradientArgs node have the same output and use Const replacement')

        # convert BroadcastGradientArgs
        # output_names: BroadcastGradientArgs 输出 tensor 名, 包含 :0, :1
        # 主要逻辑: 遍历所有节点，如果节点输入是 BroadcastGradientArgs 输出 tensor 名，则替换输入名为相应 Const 节点名，value 为 输出 tensor 的值
        new_graph_def = tf.compat.v1.GraphDef()

        # define constant 0 node
        const_zero_node_name = "single_zero_float"
        const_zero_node = self._make_const_node(const_zero_node_name, types_pb2.DT_FLOAT, [0.0])
        new_graph_def.node.append(const_zero_node)

        for node in self.frozen_graph.node:
            if node.op == "BroadcastGradientArgs":
                # output1
                output1_value = values[node.name + ':0'][0]
                output1_node = self._make_const_node(node.name, types_pb2.DT_INT32, output1_value)
                new_graph_def.node.append(output1_node)

                # output2
                output2_value = values[node.name + ':1'][0]
                output2_node = self._make_const_node(node.name + '_1', types_pb2.DT_INT32, output2_value)
                new_graph_def.node.append(output2_node)

                continue

            # 如果 node 的输入是 BroadcastGradientArgs 节点的输出，则修改输入名为新的节点名
            for i in range(len(node.input)):
                if node.input[i] not in values.keys():
                    continue
                if node.input[i].find("BroadcastGradientArgs:1") != -1:
                    node.input[i] = node.input[i][:-2] + "_1"

            if node.op == "ReluGrad":
                # ReluGrad input
                gradients_name = node.input[0]
                features_name = node.input[1]

                # define Greater node
                greater_node_name = node.name + "_Greater_Zero"
                greater_node = node_def_pb2.NodeDef()
                greater_node.CopyFrom(node)
                greater_node.name = greater_node_name
                greater_node.op = "Greater"
                greater_node.input[0] = features_name
                greater_node.input[1] = const_zero_node_name
                new_graph_def.node.append(greater_node)

                # define Cast node
                cast_node_name = node.name + "_Cast_Float"
                cast_node = node_def_pb2.NodeDef()
                cast_node.name = cast_node_name
                cast_node.op = 'Cast'
                cast_node.input.append(greater_node_name)
                cast_node.attr['DstT'].type = types_pb2.DT_FLOAT
                cast_node.attr['SrcT'].type = types_pb2.DT_BOOL
                cast_node.attr['Truncate'].b = False
                new_graph_def.node.append(cast_node)

                # Mul node
                node.op = "Mul"
                node.input[1] = cast_node_name

            new_graph_def.node.append(node)

        self.frozen_graph = new_graph_def

    def clean_and_freeze_graph(self, output_path: str):
        """
        主要功能：加载tf graph，完成对graph的清理和冻结，后续需要调用split_cpu_targets做cpu targets替换
        步骤：
        1. _load_graph：解析pbtxt文件
        2. _reset_graph_device：去除pbtxt文件中写死的device
        3. _fix_control_placeholders：将control placeholder从placeholder转换为const
        4. _fix_dense_placeholder：将dense placeholder的size写入graph中
        5. _clean_graph：根据目标节点，对模型执行DFS搜索，清理掉冗余的op（如backward计算）
        6. _freeze_graph：运行权重的assign op，图冻结
        """

        self._load_graph()
        self._reset_graph_device()
        self._fix_control_placeholders()
        self._fix_dense_placeholder()
        self.graph_proto = self._clean_graph(self.graph_proto, targets=self.predict_target)
        self._save_graph(self.graph_proto, os.path.join(output_path, const.GRAPH_AFTER_CLEAN_FILENAME))

        self._freeze_graph()
        # fix sparse placeholders after graph freezing to remove gradient nodes
        self._fix_sparse_placeholder()
        self._save_graph(self.frozen_graph, os.path.join(output_path, const.GRAPH_AFTER_FUSE_FILENAME))

    def split_cpu_targets(self, output_path: str):
        """
        主要功能：在clean_and_freeze_graph后做cpu targets替换，输出self.frozen_graph供导出ONNX用
        步骤：
        7. 如果用户配置了placeholder_replace（cpu targets），会运行：
            7.1. _parse_model_inputs：记录输入节点的shape信息
            7.2. _fix_phtype_dynamic_node：根据先验，再次筛选具有动态维度的节点
            7.3. _infer_shape：使用shape信息构造输入，推理获得cpu targets节点的shape
                 _check_cpu_targets_shape：校验cpu targets 是否具有动态维度
            7.4. _split_graph：将shape写入graph，转换为placeholder，并清理孤立节点
        8. _parse_model_inputs：记录输入节点的shape信息（cpu targets会改变输入节点）
        9. _save_graph：把frozen graph保存到磁盘，方便调试
        """

        if self.placeholder_replace is not None:
            self._parse_model_inputs()
            self._fix_phtype_dynamic_node()

            # TODO: 部分模型，key_size 较小时（< 10）可能导致 cputarget 推理出的 shape 有变化，
            # 这里需要增加更多的 key_size 组合去验证
            # 对具有动态维度(key_size)的节点，进行两次赋值(极大值，方便调试查找)
            dynamic_key_size = const.CHECK_CPU_TARGET_SHAPE_KEY_SIZE_1
            shape1 = self._infer_shape(self.frozen_graph, self.placeholder_replace, dynamic_key_size)

            dynamic_key_size = const.CHECK_CPU_TARGET_SHAPE_KEY_SIZE_2
            shape2 = self._infer_shape(self.frozen_graph, self.placeholder_replace, dynamic_key_size)
            # 校验两次维度
            cpu_targets = self._check_cpu_targets_shape(shape1, shape2)
            if len(cpu_targets) != len(self.placeholder_replace):
                raise Exception('Please check you Cpu targets, Ensure that Nodes have no dynamic dimensions.')

            # 保存cpu部分计算图
            graph_cpu_infer = self._clean_graph(self.frozen_graph, targets=self.placeholder_replace)
            self._save_graph(graph_cpu_infer, os.path.join(output_path, const.GRAPH_CPU_INFER_FILENAME))

            self._split_graph(self.placeholder_replace, shape1)

        self._parse_model_inputs()
        self._fix_phtype_dynamic_node()
        self._convert_frozen_graph()

        # save graph for debugging
        self._save_graph(self.frozen_graph, os.path.join(output_path, const.FROZEN_GRAPH_FILENAME))

    def set_onnx_batch_size(self, batch_size: int, nobatch_onnx_file: str, output_onnx_file: str):
        logging.info('Set onnx batch size, nobatch onnx file: %s, batch size: %d, after set batch onnx file: %s'
                     % (nobatch_onnx_file, batch_size, output_onnx_file))
        # load onnx
        onnx_model = onnx.load_model(nobatch_onnx_file)
        for input in onnx_model.graph.input:
            if input.type.tensor_type.shape.dim[0].dim_value == 0:
                input.type.tensor_type.shape.dim[0].dim_value = batch_size
            else:
                logging.info('input %s batch has exist %d' % (input.name, input.type.tensor_type.shape.dim[0].dim_value))

        # save onnx
        onnx.save_model(onnx_model, output_onnx_file)

        # infer onnx shape
        logging.info('Running shape inference on ONNX file: %s' % output_onnx_file)
        benchmark_onnx.infer_onnx_shape(output_onnx_file)
